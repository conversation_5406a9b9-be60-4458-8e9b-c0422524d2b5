import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import sensor
from esphome.const import (
    CONF_ID, CONF_FREQUENCY, CONF_TEMPERATURE, CONF_VOLTAGE, 
    CONF_CURRENT, CONF_POWER, CONF_ENERGY, 
    DEVICE_CLASS_CURRENT, DEVICE_CLASS_ENERGY, DEVICE_CLASS_POWER,
    DEVICE_CLASS_VOLTAGE, DEVICE_CLASS_TEMPERATURE, DEVICE_CLASS_FREQUENCY,
    STATE_CLASS_MEASUREMENT, STATE_CLASS_TOTAL_INCREASING,
    UNIT_VOLT, UNIT_AMPERE, UNIT_WATT, UNIT_KILOWATT_HOURS, 
    UNIT_HERTZ, UNIT_CELSIUS
)
from . import BL0906Factory, CONF_BL0906_FACTORY_ID, bl0906_factory_ns

DEPENDENCIES = ["bl0906_factory"]

# CONF_BL0906_FACTORY_ID 从父模块导入，所以这里不需要再定义
# CONF_BL0906_FACTORY_ID = "bl0906_factory_id"  # 新增常量

# BL0906Factory 从父模块导入，所以这里不需要再定义
# from esphome.components.bl0906_factory import BL0906Factory

# 定义电流、功率、能量传感器的常量键名，如果它们没有在 esphome.const 中统一定义的话
CONF_CURRENT_1 = "current_1"
CONF_CURRENT_2 = "current_2"
CONF_CURRENT_3 = "current_3"
CONF_CURRENT_4 = "current_4"
CONF_CURRENT_5 = "current_5"
CONF_CURRENT_6 = "current_6"
CONF_POWER_1 = "power_1"
CONF_POWER_2 = "power_2"
CONF_POWER_3 = "power_3"
CONF_POWER_4 = "power_4"
CONF_POWER_5 = "power_5"
CONF_POWER_6 = "power_6"
CONF_POWER_SUM = "power_sum"
CONF_ENERGY_1 = "energy_1"
CONF_ENERGY_2 = "energy_2"
CONF_ENERGY_3 = "energy_3"
CONF_ENERGY_4 = "energy_4"
CONF_ENERGY_5 = "energy_5"
CONF_ENERGY_6 = "energy_6"
CONF_ENERGY_SUM = "energy_sum"

# 定义基础传感器常量
BASIC_SENSORS = {
    CONF_FREQUENCY: {
        "unit": UNIT_HERTZ,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_FREQUENCY,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_TEMPERATURE: {
        "unit": UNIT_CELSIUS,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_TEMPERATURE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_VOLTAGE: {
        "unit": UNIT_VOLT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_VOLTAGE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
}

# 定义多通道传感器常量
CHANNEL_COUNT = 6

# 通道型传感器配置
CURRENT_SENSORS = {f"current_{i}": {
    "unit": UNIT_AMPERE,
    "accuracy": 3,
    "device_class": DEVICE_CLASS_CURRENT,
    "state_class": STATE_CLASS_MEASUREMENT,
} for i in range(1, CHANNEL_COUNT + 1)}

POWER_SENSORS = {f"power_{i}": {
    "unit": UNIT_WATT,
    "accuracy": 1,
    "device_class": DEVICE_CLASS_POWER,
    "state_class": STATE_CLASS_MEASUREMENT,
} for i in range(1, CHANNEL_COUNT + 1)}

ENERGY_SENSORS = {f"energy_{i}": {
    "unit": UNIT_KILOWATT_HOURS,
    "accuracy": 3,
    "device_class": DEVICE_CLASS_ENERGY,
    "state_class": STATE_CLASS_TOTAL_INCREASING,
} for i in range(1, CHANNEL_COUNT + 1)}

# 添加总功率和总能耗
POWER_SENSORS["power_sum"] = {
    "unit": UNIT_WATT,
    "accuracy": 1,
    "device_class": DEVICE_CLASS_POWER,
    "state_class": STATE_CLASS_MEASUREMENT,
}

ENERGY_SENSORS["energy_sum"] = {
    "unit": UNIT_KILOWATT_HOURS,
    "accuracy": 3,
    "device_class": DEVICE_CLASS_ENERGY,
    "state_class": STATE_CLASS_TOTAL_INCREASING,
}

# 构建配置模式
CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})

# 添加基础传感器
for key, options in BASIC_SENSORS.items():
    CONFIG_SCHEMA = CONFIG_SCHEMA.extend({
        cv.Optional(key): sensor.sensor_schema(
            unit_of_measurement=options["unit"],
            accuracy_decimals=options["accuracy"],
            device_class=options["device_class"],
            state_class=options["state_class"],
        )
    })

# 添加电流传感器
for key, options in CURRENT_SENSORS.items():
    CONFIG_SCHEMA = CONFIG_SCHEMA.extend({
        cv.Optional(key): sensor.sensor_schema(
            unit_of_measurement=options["unit"],
            accuracy_decimals=options["accuracy"],
            device_class=options["device_class"],
            state_class=options["state_class"],
        )
    })

# 添加功率传感器
for key, options in POWER_SENSORS.items():
    CONFIG_SCHEMA = CONFIG_SCHEMA.extend({
        cv.Optional(key): sensor.sensor_schema(
            unit_of_measurement=options["unit"],
            accuracy_decimals=options["accuracy"],
            device_class=options["device_class"],
            state_class=options["state_class"],
        )
    })

# 添加能量传感器
for key, options in ENERGY_SENSORS.items():
    CONFIG_SCHEMA = CONFIG_SCHEMA.extend({
        cv.Optional(key): sensor.sensor_schema(
            unit_of_measurement=options["unit"],
            accuracy_decimals=options["accuracy"],
            device_class=options["device_class"],
            state_class=options["state_class"],
        )
    })

async def to_code(config):
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 注册基本传感器
    for sensor_key in BASIC_SENSORS:
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            cg.add(getattr(var, f"set_{sensor_key}_sensor")(sens))
    
    # 注册电流传感器
    for i in range(1, CHANNEL_COUNT + 1):
        sensor_key = f"current_{i}"
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            cg.add(getattr(var, f"set_current_{i}_sensor")(sens))
    
    # 注册功率传感器
    for i in range(1, CHANNEL_COUNT + 1):
        sensor_key = f"power_{i}"
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            cg.add(getattr(var, f"set_power_{i}_sensor")(sens))
    
    # 注册总功率传感器
    if "power_sum" in config:
        sens = await sensor.new_sensor(config["power_sum"])
        cg.add(var.set_power_sum_sensor(sens))
    
    # 注册能量传感器
    for i in range(1, CHANNEL_COUNT + 1):
        sensor_key = f"energy_{i}"
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            cg.add(getattr(var, f"set_energy_{i}_sensor")(sens))
    
    # 注册总能量传感器
    if "energy_sum" in config:
        sens = await sensor.new_sensor(config["energy_sum"])
        cg.add(var.set_energy_sum_sensor(sens))
    