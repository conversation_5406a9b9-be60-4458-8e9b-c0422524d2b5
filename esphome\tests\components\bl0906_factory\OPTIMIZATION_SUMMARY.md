# BL0906 Factory 组件代码优化总结

## 优化概述

本次优化主要针对 BL0906 Factory 组件中的冗余代码进行了清理和重构，显著减少了代码重复，提高了代码的可维护性和可读性。

## 主要优化内容

### 1. 头文件优化 (bl0906_factory.h)

#### 1.1 传感器设置方法优化
**优化前：**
- 23个独立的传感器设置方法（current_1_sensor 到 current_6_sensor 等）
- 每个方法都是重复的模式

**优化后：**
- 引入统一的通道传感器设置方法：
  - `set_current_sensor(int channel, sensor::Sensor *sensor)`
  - `set_power_sensor(int channel, sensor::Sensor *sensor)`
  - `set_energy_sensor(int channel, sensor::Sensor *sensor)`
- 保留原有方法作为向后兼容的包装器

#### 1.2 传感器指针声明优化
**优化前：**
- 23个独立的传感器指针声明
- 重复的传感器数组声明

**优化后：**
- 使用数组存储通道传感器：
  - `sensor::Sensor *current_sensors_[6]`
  - `sensor::Sensor *power_sensors_[6]`
  - `sensor::Sensor *energy_sensors_[6]`
- 移除重复的数组声明

#### 1.3 传感器获取方法优化
**优化前：**
- 复杂的数组填充逻辑
- 重复的传感器指针赋值

**优化后：**
- 直接返回传感器数组
- 简化的 `get_current_sensor()` 方法

### 2. 源文件优化 (bl0906_factory.cpp)

#### 2.1 状态机循环优化
**优化前：**
- 硬编码的寄存器地址
- 重复的传感器指针访问

**优化后：**
- 使用寄存器数组：`BL0906_I_RMS[0-5]`, `BL0906_WATT[0-5]`, `BL0906_CF_CNT[0-5]`
- 使用传感器数组：`current_sensors_[0-5]`, `power_sensors_[0-5]`, `energy_sensors_[0-5]`

### 3. Python 文件优化

#### 3.1 sensor.py 优化
**优化前：**
- 4个重复的传感器配置循环
- 重复的传感器注册代码

**优化后：**
- 统一的 `add_sensor_configs()` 函数
- 统一的 `register_sensor()` 异步函数
- 简化的循环结构

#### 3.2 number.py 优化
**优化前：**
- 重复的 Number 配置循环
- 冗长的 to_code 函数（91行）

**优化后：**
- 统一的 `add_number_configs()` 函数
- 统一的 `register_number()` 异步函数
- 简化的 to_code 函数（28行）

#### 3.3 __init__.py 优化
**优化前：**
- 重复的通道常量定义
- 重复的校准配置模式定义

**优化后：**
- 动态生成通道常量：`CHANNEL_CONFIGS`
- 动态构建校准配置：`build_calibration_schema()`

## 优化效果

### 代码行数减少
- **bl0906_factory.h**: 减少约 30 行重复代码
- **sensor.py**: 减少约 25 行重复代码
- **number.py**: 减少约 65 行重复代码
- **__init__.py**: 减少约 10 行重复代码

### 可维护性提升
1. **统一的接口**: 所有通道传感器使用统一的设置和访问方法
2. **减少错误**: 消除了手动维护多个相似方法的风险
3. **易于扩展**: 新增通道只需修改常量，无需添加新方法
4. **代码复用**: 通过辅助函数减少重复逻辑

### 性能优化
1. **内存使用**: 使用数组替代多个独立指针，减少内存碎片
2. **访问效率**: 数组访问比多个条件判断更高效
3. **编译优化**: 减少重复代码有利于编译器优化

## 向后兼容性

所有优化都保持了向后兼容性：
- 原有的 API 接口保持不变
- 现有的配置文件无需修改
- 功能行为完全一致

## 建议的后续优化

1. **进一步模板化**: 可以考虑使用 C++ 模板进一步减少重复代码
2. **配置验证**: 增强配置验证逻辑，提供更好的错误提示
3. **文档更新**: 更新相关文档以反映新的代码结构
4. **单元测试**: 添加单元测试确保优化后的代码正确性

## 总结

本次优化成功消除了大量冗余代码，提高了代码质量和可维护性。优化后的代码更加简洁、高效，同时保持了完全的向后兼容性。这为后续的功能扩展和维护工作奠定了良好的基础。
