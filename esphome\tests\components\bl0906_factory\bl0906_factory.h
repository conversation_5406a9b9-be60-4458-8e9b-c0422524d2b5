#pragma once

#include "esphome/core/component.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/sensor/sensor.h"
#include "bl0906_calibration.h"
#include "bl0906_registers.h"
#include "esphome/components/number/number.h"
#include "esphome/components/uart/uart_component.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "esphome/core/defines.h"
#include "esphome/core/log.h"
#include "esphome/core/time.h"
#include "esphome/components/sensor/sensor.h"
#include <map>
#include "bl0906_number.h"

namespace esphome {
namespace bl0906_factory {

// 硬件资料
// https://www.belling.com.cn/media/file_object/bel_product/BL0906/guide/BL0906%20APP%20Note_V1.02.pdf
// https://www.belling.com.cn/media/file_object/bel_product/BL0906/datasheet/BL0906_V1.02_cn.pdf

// 注意：字节顺序为大端模式(big endian)：数据的高位字节保存在内存的低地址中，而低位字节保存在内存的高地址中。（低-中-高）

// 前向声明类
class BL0906Factory;
using ActionCallbackFuncPtr = void (BL0906Factory::*)();
// 定义数据包结构体，用于UART通信
struct DataPacket {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  uint8_t h;            // high byte
  uint8_t checksum;     // checksum
};

// 无符号BL0906 24位数据类型
struct ube24_t {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  uint8_t h;            // high byte
};

// 带符号BL0906 24位数据类型
struct sbe24_t {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  int8_t h;             // high byte, signed for proper sign extension
};

// 校准寄存器类型枚举
enum class CalibRegType {
  CHGN,     // 电流增益
  CHOS,     // 电流偏置
  RMSGN,    // 有效值增益
  RMSOS,    // 有效值偏置
  WATTGN,   // 功率增益
  WATTOS,   // 功率偏置
  CHGN_V,   // 电压增益
  CHOS_V    // 电压偏置
};

// 为了向后兼容，在命名空间级别定义常量
constexpr auto CHGN = CalibRegType::CHGN;
constexpr auto CHOS = CalibRegType::CHOS;
constexpr auto RMSGN = CalibRegType::RMSGN;
constexpr auto RMSOS = CalibRegType::RMSOS;
constexpr auto WATTGN = CalibRegType::WATTGN;
constexpr auto WATTOS = CalibRegType::WATTOS;
constexpr auto CHGN_V = CalibRegType::CHGN_V;
constexpr auto CHOS_V = CalibRegType::CHOS_V;

// 在BL0906Factory类声明之前声明BL0906Number类
class BL0906Number;

class BL0906Factory : public PollingComponent, public uart::UARTDevice {
 public:

  static const char *const BL0906_FACTORY_ID; // 原BL0906_CALIB_ID

  enum class State {
    IDLE,
    READ_BASIC_SENSORS,     // 温度、频率、电压
    READ_CHANNEL_DATA,      // 通道数据（使用循环）
    READ_TOTAL_DATA,        // 总功率和能量
    HANDLE_ACTIONS
  };

  // 现代化传感器管理 - 使用枚举和模板
  enum class SensorType {
    VOLTAGE,
    FREQUENCY,
    TEMPERATURE,
    CURRENT,
    POWER,
    ENERGY,
    POWER_SUM,
    ENERGY_SUM
  };

  // 统一的传感器设置接口
  void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0) {
    switch (type) {
      case SensorType::VOLTAGE:
        voltage_sensor_ = sensor;
        break;
      case SensorType::FREQUENCY:
        frequency_sensor_ = sensor;
        break;
      case SensorType::TEMPERATURE:
        temperature_sensor_ = sensor;
        break;
      case SensorType::CURRENT:
        if (is_valid_channel(channel)) {
          current_sensors_[channel] = sensor;
        }
        break;
      case SensorType::POWER:
        if (is_valid_channel(channel)) {
          power_sensors_[channel] = sensor;
        }
        break;
      case SensorType::ENERGY:
        if (is_valid_channel(channel)) {
          energy_sensors_[channel] = sensor;
        }
        break;
      case SensorType::POWER_SUM:
        power_sum_sensor_ = sensor;
        break;
      case SensorType::ENERGY_SUM:
        energy_sum_sensor_ = sensor;
        break;
    }
  }

  // 获取传感器的统一接口
  sensor::Sensor* get_sensor(SensorType type, int channel = 0) const {
    switch (type) {
      case SensorType::VOLTAGE: return voltage_sensor_;
      case SensorType::FREQUENCY: return frequency_sensor_;
      case SensorType::TEMPERATURE: return temperature_sensor_;
      case SensorType::CURRENT:
        return is_valid_channel(channel) ? current_sensors_[channel] : nullptr;
      case SensorType::POWER:
        return is_valid_channel(channel) ? power_sensors_[channel] : nullptr;
      case SensorType::ENERGY:
        return is_valid_channel(channel) ? energy_sensors_[channel] : nullptr;
      case SensorType::POWER_SUM: return power_sum_sensor_;
      case SensorType::ENERGY_SUM: return energy_sum_sensor_;
      default: return nullptr;
    }
  }
  // 辅助方法
  bool is_valid_channel(int channel) const {
    return channel >= 0 && channel < CHANNEL_COUNT;
  }

  // 批量设置通道传感器
  void set_channel_sensors(SensorType type, const std::vector<sensor::Sensor*>& sensors) {
    for (size_t i = 0; i < sensors.size() && i < CHANNEL_COUNT; ++i) {
      set_sensor(type, sensors[i], i);
    }
  }

  // 获取所有通道传感器
  std::vector<sensor::Sensor*> get_channel_sensors(SensorType type) const {
    std::vector<sensor::Sensor*> sensors;
    for (int i = 0; i < CHANNEL_COUNT; ++i) {
      sensors.push_back(get_sensor(type, i));
    }
    return sensors;
  }

  // 现代化校准Number组件管理
  enum class CalibNumberType {
    CHGN,      // 电流增益
    CHOS,      // 电流偏置
    RMSGN,     // 有效值增益
    RMSOS,     // 有效值偏置
    WATTGN,    // 功率增益
    WATTOS,    // 功率偏置
    CHGN_V,    // 电压增益
    CHOS_V     // 电压偏置
  };

  // 统一的校准Number设置接口
  void set_calib_number(CalibNumberType type, number::Number *num, int channel = -1) {
    // 使用map存储，key为类型和通道的组合
    uint32_t key = static_cast<uint32_t>(type) << 8 | (channel & 0xFF);
    calib_numbers_map_[key] = num;

    // 同时添加到向量中以保持兼容性
    if (num) {
      calib_numbers_.push_back(static_cast<BL0906Number*>(num));
    }
  }

  // 获取校准Number组件
  number::Number* get_calib_number(CalibNumberType type, int channel = -1) const {
    uint32_t key = static_cast<uint32_t>(type) << 8 | (channel & 0xFF);
    auto it = calib_numbers_map_.find(key);
    return (it != calib_numbers_map_.end()) ? it->second : nullptr;
  }

  // 简化的校准寄存器初始值设置方法，使用一个通用方法替代多个单独的方法
  void set_initial_calib_value(CalibRegType type, int channel, int16_t value) {
    uint8_t reg_addr = get_register_address(type, channel);
    if (reg_addr != 0) {
      initial_calibration_values_[reg_addr] = value;
    }
  }


  // 获取校准寄存器地址的统一方法
  uint8_t get_register_address(CalibRegType type, int channel) {
    // 通道范围检查
    if (channel < 0 || channel > 6) return 0;

    // 使用寄存器数组获取地址
    switch(type) {
      case CalibRegType::CHGN:
        return (channel == 0) ? BL0906_CHGN_V : BL0906_CHGN[channel - 1];

      case CalibRegType::CHOS:
        return (channel == 0) ? BL0906_CHOS_V : BL0906_CHOS[channel - 1];

      case CalibRegType::RMSGN:
        return (channel == 0) ? BL0906_RMSGN_V : BL0906_RMSGN[channel - 1];

      case CalibRegType::RMSOS:
        return (channel == 0) ? BL0906_RMSOS_V : BL0906_RMSOS[channel - 1];

      case CalibRegType::WATTGN:
        return (channel == 0) ? 0 : BL0906_WATTGN[channel - 1];

      case CalibRegType::WATTOS:
        return (channel == 0) ? 0 : BL0906_WATTOS[channel - 1];

      case CalibRegType::CHGN_V:
        return BL0906_CHGN_V;

      case CalibRegType::CHOS_V:
        return BL0906_CHOS_V;

      default:
        return 0;
    }
  }

  void loop() override;
  void update();
  void setup() override;
  BL0906Factory();
  // 对外公开读写寄存器接口(供Number组件使用)
  int32_t read_register_value(uint8_t address);
  bool write_register_value(uint8_t address, int16_t value);

  // 读数据方法
  void read_data_(uint8_t address, float reference, sensor::Sensor *sensor);

  // 判断寄存器是否属于CHGN或CHOS系列
  bool is_16bit_register(uint8_t address);

  // 添加方法用于注册校准数字组件
  void register_calib_number(BL0906Number *number);

  // 添加刷新所有校准数字组件的方法
  void refresh_all_calib_numbers();

  // 添加写保护相关方法
  bool turn_off_write_protect();

  // 新增辅助函数
  void flush_rx_buffer();
  bool send_read_command_and_receive(uint8_t address, DataPacket &data);
  bool send_write_command(uint8_t reg, uint8_t l, uint8_t m, uint8_t h);

  // 应用校准值的方法
  void apply_calibration_values();

 protected:
  // 状态相关变量
  State current_state_{State::IDLE};
  uint8_t current_channel_{0};

  // 操作队列相关方法
  size_t enqueue_action_(ActionCallbackFuncPtr function);
  void handle_actions_();
  std::vector<ActionCallbackFuncPtr> action_queue_{};

  // 存储YAML配置中的初始校准值
  std::map<uint8_t, int16_t> initial_calibration_values_{};

  // 现代化传感器存储 - 使用数组和常量
  static constexpr int CHANNEL_COUNT = 6;

  // 基础传感器
  sensor::Sensor *voltage_sensor_{nullptr};
  sensor::Sensor *frequency_sensor_{nullptr};
  sensor::Sensor *temperature_sensor_{nullptr};
  sensor::Sensor *power_sum_sensor_{nullptr};
  sensor::Sensor *energy_sum_sensor_{nullptr};

  // 通道传感器数组（索引0-5对应通道0-5）
  sensor::Sensor *current_sensors_[CHANNEL_COUNT]{nullptr};
  sensor::Sensor *power_sensors_[CHANNEL_COUNT]{nullptr};
  sensor::Sensor *energy_sensors_[CHANNEL_COUNT]{nullptr};

  // 辅助函数
  bool wait_until_available(size_t len, uint32_t timeout_ms);
  static uint32_t to_uint32_t(ube24_t input);
  static int32_t to_int32_t(sbe24_t input);

  // 现代化数据读取方法
  void read_channel_data(int channel);
  void read_basic_sensors();
  void read_calib_register(CalibNumberType type, int channel = -1);

 public:
  static BL0906Factory *bl0906_instance;  // 添加静态实例指针
  static void set_instance(BL0906Factory *instance) {
    bl0906_instance = instance;
  }

  // 添加一个新的方法用于初始化互斥锁
  void init_mutex() {
    if (mutex_ == nullptr) {
      mutex_ = xSemaphoreCreateMutex();
      if (mutex_ == nullptr) {
        ESP_LOGE("bl0906_factory", "Failed to create mutex");
      }
    }
  }

  // 获取互斥锁
  bool lock(int timeout_ms = 100) {
    if (mutex_ == nullptr) {
      ESP_LOGW("bl0906_factory", "Mutex not initialized");
      return false;
    }
    return xSemaphoreTake(mutex_, timeout_ms / portTICK_PERIOD_MS) == pdTRUE;
  }

  // 释放互斥锁
  void unlock() {
    if (mutex_ != nullptr) {
      xSemaphoreGive(mutex_);
    }
  }

  // 现代化成员变量
  std::vector<BL0906Number *> calib_numbers_;
  std::map<uint32_t, number::Number*> calib_numbers_map_;  // 新的校准Number存储

  // 状态机相关
  int current_channel_{0};  // 当前处理的通道

  // 互斥锁
  SemaphoreHandle_t mutex_{nullptr};
};

// 合并BL0906FactoryNumber和BL0906Number为BL0906Number，保留所有接口和功能
// 已移至bl0906_number.h

}  // namespace bl0906_factory
}  // namespace esphome
