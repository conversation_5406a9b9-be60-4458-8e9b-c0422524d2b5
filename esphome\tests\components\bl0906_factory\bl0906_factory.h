#pragma once

#include "esphome/core/component.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/sensor/sensor.h"
#include "bl0906_calibration.h"
#include "bl0906_registers.h"
#include "esphome/components/number/number.h"
#include "esphome/components/uart/uart_component.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "esphome/core/defines.h"
#include "esphome/core/log.h"
#include "esphome/core/time.h"
#include "esphome/components/sensor/sensor.h"
#include <map>
#include "bl0906_number.h"

namespace esphome {
namespace bl0906_factory {

// 硬件资料
// https://www.belling.com.cn/media/file_object/bel_product/BL0906/guide/BL0906%20APP%20Note_V1.02.pdf
// https://www.belling.com.cn/media/file_object/bel_product/BL0906/datasheet/BL0906_V1.02_cn.pdf

// 注意：字节顺序为大端模式(big endian)：数据的高位字节保存在内存的低地址中，而低位字节保存在内存的高地址中。（低-中-高）

// 前向声明类
class BL0906Factory;
using ActionCallbackFuncPtr = void (BL0906Factory::*)();
// 定义数据包结构体，用于UART通信
struct DataPacket {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  uint8_t h;            // high byte
  uint8_t checksum;     // checksum
};

// 无符号BL0906 24位数据类型
struct ube24_t {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  uint8_t h;            // high byte
};

// 带符号BL0906 24位数据类型
struct sbe24_t {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  int8_t h;             // high byte, signed for proper sign extension
};

// 校准寄存器类型枚举
enum class CalibRegType {
  CHGN,     // 电流增益
  CHOS,     // 电流偏置
  RMSGN,    // 有效值增益
  RMSOS,    // 有效值偏置
  WATTGN,   // 功率增益
  WATTOS,   // 功率偏置
  CHGN_V,   // 电压增益
  CHOS_V    // 电压偏置
};

// 为了向后兼容，在命名空间级别定义常量
constexpr auto CHGN = CalibRegType::CHGN;
constexpr auto CHOS = CalibRegType::CHOS;
constexpr auto RMSGN = CalibRegType::RMSGN;
constexpr auto RMSOS = CalibRegType::RMSOS;
constexpr auto WATTGN = CalibRegType::WATTGN;
constexpr auto WATTOS = CalibRegType::WATTOS;
constexpr auto CHGN_V = CalibRegType::CHGN_V;
constexpr auto CHOS_V = CalibRegType::CHOS_V;

// 在BL0906Factory类声明之前声明BL0906Number类
class BL0906Number;

class BL0906Factory : public PollingComponent, public uart::UARTDevice {
 public:

  static const char *const BL0906_FACTORY_ID; // 原BL0906_CALIB_ID

  enum class State {
    IDLE,
    READ_TEMPERATURE,
    READ_CHANNEL_1,
    READ_CHANNEL_2,
    READ_CHANNEL_3,
    READ_CHANNEL_4,
    READ_CHANNEL_5,
    READ_CHANNEL_6,
    READ_FREQUENCY_VOLTAGE,
    READ_TOTAL_POWER_ENERGY,
    HANDLE_ACTIONS
  };

  void set_voltage_sensor(sensor::Sensor *sensor) { voltage_sensor_ = sensor; }
  void set_current_1_sensor(sensor::Sensor *sensor) { current_1_sensor_ = sensor; }
  void set_current_2_sensor(sensor::Sensor *sensor) { current_2_sensor_ = sensor; }
  void set_current_3_sensor(sensor::Sensor *sensor) { current_3_sensor_ = sensor; }
  void set_current_4_sensor(sensor::Sensor *sensor) { current_4_sensor_ = sensor; }
  void set_current_5_sensor(sensor::Sensor *sensor) { current_5_sensor_ = sensor; }
  void set_current_6_sensor(sensor::Sensor *sensor) { current_6_sensor_ = sensor; }
  void set_power_1_sensor(sensor::Sensor *sensor) { power_1_sensor_ = sensor; }
  void set_power_2_sensor(sensor::Sensor *sensor) { power_2_sensor_ = sensor; }
  void set_power_3_sensor(sensor::Sensor *sensor) { power_3_sensor_ = sensor; }
  void set_power_4_sensor(sensor::Sensor *sensor) { power_4_sensor_ = sensor; }
  void set_power_5_sensor(sensor::Sensor *sensor) { power_5_sensor_ = sensor; }
  void set_power_6_sensor(sensor::Sensor *sensor) { power_6_sensor_ = sensor; }
  void set_power_sum_sensor(sensor::Sensor *power_sum) { power_sum_sensor_ = power_sum; }
  void set_energy_1_sensor(sensor::Sensor *sensor) { energy_1_sensor_ = sensor; }
  void set_energy_2_sensor(sensor::Sensor *sensor) { energy_2_sensor_ = sensor; }
  void set_energy_3_sensor(sensor::Sensor *sensor) { energy_3_sensor_ = sensor; }
  void set_energy_4_sensor(sensor::Sensor *sensor) { energy_4_sensor_ = sensor; }
  void set_energy_5_sensor(sensor::Sensor *sensor) { energy_5_sensor_ = sensor; }
  void set_energy_6_sensor(sensor::Sensor *sensor) { energy_6_sensor_ = sensor; }
  void set_energy_sum_sensor(sensor::Sensor *energy_sum) { energy_sum_sensor_ = energy_sum; }
  void set_frequency_sensor(sensor::Sensor *sensor) { frequency_sensor_ = sensor; }
  void set_temperature_sensor(sensor::Sensor *sensor) { temperature_sensor_ = sensor; }
  void set_chgn_1_sensor(sensor::Sensor *sensor) { chgn_1_sensor_ = sensor; }
  void set_chgn_2_sensor(sensor::Sensor *sensor) { chgn_2_sensor_ = sensor; }
  void set_chgn_3_sensor(sensor::Sensor *sensor) { chgn_3_sensor_ = sensor; }
  void set_chgn_4_sensor(sensor::Sensor *sensor) { chgn_4_sensor_ = sensor; }
  void set_chgn_5_sensor(sensor::Sensor *sensor) { chgn_5_sensor_ = sensor; }
  void set_chgn_6_sensor(sensor::Sensor *sensor) { chgn_6_sensor_ = sensor; }
  void set_chgn_v_sensor(sensor::Sensor *sensor) { chgn_v_sensor_ = sensor; }
  void set_chos_v_sensor(sensor::Sensor *sensor) { chos_v_sensor_ = sensor; }
  void set_chos_1_sensor(sensor::Sensor *sensor) { chos_1_sensor_ = sensor; }
  void set_chos_2_sensor(sensor::Sensor *sensor) { chos_2_sensor_ = sensor; }
  void set_chos_3_sensor(sensor::Sensor *sensor) { chos_3_sensor_ = sensor; }
  void set_chos_4_sensor(sensor::Sensor *sensor) { chos_4_sensor_ = sensor; }
  void set_chos_5_sensor(sensor::Sensor *sensor) { chos_5_sensor_ = sensor; }
  void set_chos_6_sensor(sensor::Sensor *sensor) { chos_6_sensor_ = sensor; }
  void set_rmsos_1_sensor(sensor::Sensor *sensor) { rmsos_1_sensor_ = sensor; }
  void set_rmsos_2_sensor(sensor::Sensor *sensor) { rmsos_2_sensor_ = sensor; }
  void set_rmsos_3_sensor(sensor::Sensor *sensor) { rmsos_3_sensor_ = sensor; }
  void set_rmsos_4_sensor(sensor::Sensor *sensor) { rmsos_4_sensor_ = sensor; }
  void set_rmsos_5_sensor(sensor::Sensor *sensor) { rmsos_5_sensor_ = sensor; }
  void set_rmsos_6_sensor(sensor::Sensor *sensor) { rmsos_6_sensor_ = sensor; }
  void set_rmsgn_1_sensor(sensor::Sensor *sensor) { rmsgn_1_sensor_ = sensor; }
  void set_rmsgn_2_sensor(sensor::Sensor *sensor) { rmsgn_2_sensor_ = sensor; }
  void set_rmsgn_3_sensor(sensor::Sensor *sensor) { rmsgn_3_sensor_ = sensor; }
  void set_rmsgn_4_sensor(sensor::Sensor *sensor) { rmsgn_4_sensor_ = sensor; }
  void set_rmsgn_5_sensor(sensor::Sensor *sensor) { rmsgn_5_sensor_ = sensor; }
  void set_rmsgn_6_sensor(sensor::Sensor *sensor) { rmsgn_6_sensor_ = sensor; }
  void set_wattgn_1_sensor(sensor::Sensor *sensor) { wattgn_1_sensor_ = sensor; }
  void set_wattgn_2_sensor(sensor::Sensor *sensor) { wattgn_2_sensor_ = sensor; }
  void set_wattgn_3_sensor(sensor::Sensor *sensor) { wattgn_3_sensor_ = sensor; }
  void set_wattgn_4_sensor(sensor::Sensor *sensor) { wattgn_4_sensor_ = sensor; }
  void set_wattgn_5_sensor(sensor::Sensor *sensor) { wattgn_5_sensor_ = sensor; }
  void set_wattgn_6_sensor(sensor::Sensor *sensor) { wattgn_6_sensor_ = sensor; }

  // 为Number组件添加设置函数

  // 新增缺失的 CHGN Decimal Number Setters
  void set_chgn_decimal_1_number(number::Number *num) { chgn_decimal_1_number_ = num; }
  void set_chgn_decimal_2_number(number::Number *num) { chgn_decimal_2_number_ = num; }
  void set_chgn_decimal_3_number(number::Number *num) { chgn_decimal_3_number_ = num; }
  void set_chgn_decimal_4_number(number::Number *num) { chgn_decimal_4_number_ = num; }
  void set_chgn_decimal_5_number(number::Number *num) { chgn_decimal_5_number_ = num; }
  void set_chgn_decimal_6_number(number::Number *num) { chgn_decimal_6_number_ = num; }
  void set_chgn_v_decimal_number(number::Number *num) { chgn_v_decimal_number_ = num; }

  // 新增缺失的 CHOS Decimal Number Setters
  void set_chos_decimal_1_number(number::Number *num) { chos_decimal_1_number_ = num; }
  void set_chos_decimal_2_number(number::Number *num) { chos_decimal_2_number_ = num; }
  void set_chos_decimal_3_number(number::Number *num) { chos_decimal_3_number_ = num; }
  void set_chos_decimal_4_number(number::Number *num) { chos_decimal_4_number_ = num; }
  void set_chos_decimal_5_number(number::Number *num) { chos_decimal_5_number_ = num; }
  void set_chos_decimal_6_number(number::Number *num) { chos_decimal_6_number_ = num; }
  void set_chos_v_decimal_number(number::Number *num) { chos_v_decimal_number_ = num; }

  // 简化的校准寄存器初始值设置方法，使用一个通用方法替代多个单独的方法
  void set_initial_calib_value(CalibRegType type, int channel, int16_t value) {
    uint8_t reg_addr = get_register_address(type, channel);
    if (reg_addr != 0) {
      initial_calibration_values_[reg_addr] = value;
    }
  }
  
  
  // 获取校准寄存器地址的统一方法
  uint8_t get_register_address(CalibRegType type, int channel) {
    // 通道范围检查
    if (channel < 0 || channel > 6) return 0;
    
    // 使用寄存器数组获取地址
    switch(type) {
      case CalibRegType::CHGN:
        return (channel == 0) ? BL0906_CHGN_V : BL0906_CHGN[channel - 1];
      
      case CalibRegType::CHOS:
        return (channel == 0) ? BL0906_CHOS_V : BL0906_CHOS[channel - 1];
      
      case CalibRegType::RMSGN:
        return (channel == 0) ? BL0906_RMSGN_V : BL0906_RMSGN[channel - 1];
      
      case CalibRegType::RMSOS:
        return (channel == 0) ? BL0906_RMSOS_V : BL0906_RMSOS[channel - 1];
      
      case CalibRegType::WATTGN:
        return (channel == 0) ? 0 : BL0906_WATTGN[channel - 1];
      
      case CalibRegType::WATTOS:
        return (channel == 0) ? 0 : BL0906_WATTOS[channel - 1];
      
      case CalibRegType::CHGN_V:
        return BL0906_CHGN_V;
      
      case CalibRegType::CHOS_V:
        return BL0906_CHOS_V;
      
      default:
        return 0;
    }
  }

  void loop() override;
  void update();
  void setup() override;
  BL0906Factory();
  // 对外公开读写寄存器接口(供Number组件使用)
  int32_t read_register_value(uint8_t address);
  bool write_register_value(uint8_t address, int16_t value);

  // 读数据方法
  void read_data_(uint8_t address, float reference, sensor::Sensor *sensor);

  // 判断寄存器是否属于CHGN或CHOS系列
  bool is_16bit_register(uint8_t address);

  // 添加方法用于注册校准数字组件
  void register_calib_number(BL0906Number *number);

  // 添加刷新所有校准数字组件的方法
  void refresh_all_calib_numbers();

  // 添加写保护相关方法
  bool turn_off_write_protect();

  // 新增辅助函数
  void flush_rx_buffer();
  bool send_read_command_and_receive(uint8_t address, DataPacket &data);
  bool send_write_command(uint8_t reg, uint8_t l, uint8_t m, uint8_t h);
  
  // 应用校准值的方法
  void apply_calibration_values();

 protected:
  // 状态相关变量
  State current_state_{State::IDLE};
  uint8_t current_channel_{0};

  // 操作队列相关方法
  size_t enqueue_action_(ActionCallbackFuncPtr function);
  void handle_actions_();
  std::vector<ActionCallbackFuncPtr> action_queue_{};
  
  // 存储YAML配置中的初始校准值
  std::map<uint8_t, int16_t> initial_calibration_values_{};

  // 传感器指针
  sensor::Sensor *voltage_sensor_{nullptr};
  sensor::Sensor *current_1_sensor_{nullptr};
  sensor::Sensor *current_2_sensor_{nullptr};
  sensor::Sensor *current_3_sensor_{nullptr};
  sensor::Sensor *current_4_sensor_{nullptr};
  sensor::Sensor *current_5_sensor_{nullptr};
  sensor::Sensor *current_6_sensor_{nullptr};
  sensor::Sensor *power_1_sensor_{nullptr};
  sensor::Sensor *power_2_sensor_{nullptr};
  sensor::Sensor *power_3_sensor_{nullptr};
  sensor::Sensor *power_4_sensor_{nullptr};
  sensor::Sensor *power_5_sensor_{nullptr};
  sensor::Sensor *power_6_sensor_{nullptr};
  sensor::Sensor *power_sum_sensor_{nullptr};
  sensor::Sensor *energy_1_sensor_{nullptr};
  sensor::Sensor *energy_2_sensor_{nullptr};
  sensor::Sensor *energy_3_sensor_{nullptr};
  sensor::Sensor *energy_4_sensor_{nullptr};
  sensor::Sensor *energy_5_sensor_{nullptr};
  sensor::Sensor *energy_6_sensor_{nullptr};
  sensor::Sensor *energy_sum_sensor_{nullptr};
  sensor::Sensor *frequency_sensor_{nullptr};
  sensor::Sensor *temperature_sensor_{nullptr};
  sensor::Sensor *chgn_1_sensor_{nullptr};
  sensor::Sensor *chgn_2_sensor_{nullptr};
  sensor::Sensor *chgn_3_sensor_{nullptr};
  sensor::Sensor *chgn_4_sensor_{nullptr};
  sensor::Sensor *chgn_5_sensor_{nullptr};
  sensor::Sensor *chgn_6_sensor_{nullptr};
  sensor::Sensor *chgn_v_sensor_{nullptr};
  sensor::Sensor *chos_v_sensor_{nullptr};
  sensor::Sensor *chos_1_sensor_{nullptr};
  sensor::Sensor *chos_2_sensor_{nullptr};
  sensor::Sensor *chos_3_sensor_{nullptr};
  sensor::Sensor *chos_4_sensor_{nullptr};
  sensor::Sensor *chos_5_sensor_{nullptr};
  sensor::Sensor *chos_6_sensor_{nullptr};
  sensor::Sensor *rmsos_1_sensor_{nullptr};
  sensor::Sensor *rmsos_2_sensor_{nullptr};
  sensor::Sensor *rmsos_3_sensor_{nullptr};
  sensor::Sensor *rmsos_4_sensor_{nullptr};
  sensor::Sensor *rmsos_5_sensor_{nullptr};
  sensor::Sensor *rmsos_6_sensor_{nullptr};
  sensor::Sensor *rmsgn_1_sensor_{nullptr};
  sensor::Sensor *rmsgn_2_sensor_{nullptr};
  sensor::Sensor *rmsgn_3_sensor_{nullptr};
  sensor::Sensor *rmsgn_4_sensor_{nullptr};
  sensor::Sensor *rmsgn_5_sensor_{nullptr};
  sensor::Sensor *rmsgn_6_sensor_{nullptr};
  sensor::Sensor *wattgn_1_sensor_{nullptr};
  sensor::Sensor *wattgn_2_sensor_{nullptr};
  sensor::Sensor *wattgn_3_sensor_{nullptr};
  sensor::Sensor *wattgn_4_sensor_{nullptr};
  sensor::Sensor *wattgn_5_sensor_{nullptr};
  sensor::Sensor *wattgn_6_sensor_{nullptr};

  // 辅助函数
  bool wait_until_available(size_t len, uint32_t timeout_ms);
  static uint32_t to_uint32_t(ube24_t input);
  static int32_t to_int32_t(sbe24_t input);

  // 模板函数声明
  template<size_t N>
  void read_calib_registers(const char* reg_type_name,const std::array<std::tuple<uint8_t, sensor::Sensor*, int>, N>& items);

  std::vector<BL0906Number *> calib_numbers_;

 public:
  static BL0906Factory *bl0906_instance;  // 添加静态实例指针
  static void set_instance(BL0906Factory *instance) {
    bl0906_instance = instance;
  }

  // 添加一个新的方法用于初始化互斥锁
  void init_mutex() {
    if (mutex_ == nullptr) {
      mutex_ = xSemaphoreCreateMutex();
      if (mutex_ == nullptr) {
        ESP_LOGE("bl0906_factory", "Failed to create mutex");
      }
    }
  }

  // 获取互斥锁
  bool lock(int timeout_ms = 100) {
    if (mutex_ == nullptr) {
      ESP_LOGW("bl0906_factory", "Mutex not initialized");
      return false;
    }
    return xSemaphoreTake(mutex_, timeout_ms / portTICK_PERIOD_MS) == pdTRUE;
  }

  // 释放互斥锁
  void unlock() {
    if (mutex_ != nullptr) {
      xSemaphoreGive(mutex_);
    }
  }

  // 获取传感器数组
  sensor::Sensor **get_current_sensors() {
    // 使用类成员变量而不是静态变量，避免竞争条件
    current_sensors_[0] = current_1_sensor_;
    current_sensors_[1] = current_2_sensor_;
    current_sensors_[2] = current_3_sensor_;
    current_sensors_[3] = current_4_sensor_;
    current_sensors_[4] = current_5_sensor_;
    current_sensors_[5] = current_6_sensor_;
    return current_sensors_;
  }

  sensor::Sensor **get_power_sensors() {
    power_sensors_[0] = power_1_sensor_;
    power_sensors_[1] = power_2_sensor_;
    power_sensors_[2] = power_3_sensor_;
    power_sensors_[3] = power_4_sensor_;
    power_sensors_[4] = power_5_sensor_;
    power_sensors_[5] = power_6_sensor_;
    return power_sensors_;
  }

  sensor::Sensor **get_energy_sensors() {
    energy_sensors_[0] = energy_1_sensor_;
    energy_sensors_[1] = energy_2_sensor_;
    energy_sensors_[2] = energy_3_sensor_;
    energy_sensors_[3] = energy_4_sensor_;
    energy_sensors_[4] = energy_5_sensor_;
    energy_sensors_[5] = energy_6_sensor_;
    return energy_sensors_;
  }

  // 添加互斥锁保护的传感器获取方法
  sensor::Sensor *get_voltage_sensor() {
    if (lock()) {
      sensor::Sensor *sensor = voltage_sensor_;
      unlock();
      return sensor;
    }
    return voltage_sensor_;
  }

  sensor::Sensor *get_current_sensor(int channel) {
    if (channel < 1 || channel > 6) {
      return nullptr;
    }

    sensor::Sensor *sensors[] = {
      current_1_sensor_,
      current_2_sensor_,
      current_3_sensor_,
      current_4_sensor_,
      current_5_sensor_,
      current_6_sensor_
    };

    if (lock()) {
      sensor::Sensor *sensor = sensors[channel - 1];
      unlock();
      return sensor;
    }
    return sensors[channel - 1];
  }

  // 传感器数组成员变量
  sensor::Sensor *current_sensors_[6]{nullptr};
  sensor::Sensor *power_sensors_[6]{nullptr};
  sensor::Sensor *energy_sensors_[6]{nullptr};

 

  // 新增对应的成员变量指针 for CHGN/CHOS Decimal Numbers
  number::Number *chgn_decimal_1_number_{nullptr};
  number::Number *chgn_decimal_2_number_{nullptr};
  number::Number *chgn_decimal_3_number_{nullptr};
  number::Number *chgn_decimal_4_number_{nullptr};
  number::Number *chgn_decimal_5_number_{nullptr};
  number::Number *chgn_decimal_6_number_{nullptr};
  number::Number *chgn_v_decimal_number_{nullptr};
  number::Number *chos_decimal_1_number_{nullptr};
  number::Number *chos_decimal_2_number_{nullptr};
  number::Number *chos_decimal_3_number_{nullptr};
  number::Number *chos_decimal_4_number_{nullptr};
  number::Number *chos_decimal_5_number_{nullptr};
  number::Number *chos_decimal_6_number_{nullptr};
  number::Number *chos_v_decimal_number_{nullptr};

  // 互斥锁
  SemaphoreHandle_t mutex_{nullptr};
};

// 合并BL0906FactoryNumber和BL0906Number为BL0906Number，保留所有接口和功能
// 已移至bl0906_number.h

}  // namespace bl0906_factory
}  // namespace esphome
