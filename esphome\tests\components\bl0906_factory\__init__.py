"""BL0906_carrot8848 自定义电能计量组件"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, number
from esphome import automation
from esphome.const import (
    CONF_ID,
    CONF_UART_ID,
    CONF_UPDATE_INTERVAL
)

DEPENDENCIES = ["uart"]
CODEOWNERS = ["@carrot8848"]
AUTO_LOAD = ["sensor", "number"]
MULTI_CONF = True
CPP_SOURCES = ["bl0906_factory.cpp"]

# 定义命名空间
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Factory = bl0906_factory_ns.class_("BL0906Factory", cg.PollingComponent, uart.UARTDevice)
BL0906Number = bl0906_factory_ns.class_("BL0906Number", number.Number, cg.Component)

CalibRegType = bl0906_factory_ns.enum("CalibRegType")
CHGN = CalibRegType.CHGN
CHOS = CalibRegType.CHOS
RMSGN = CalibRegType.RMSGN
RMSOS = CalibRegType.RMSOS
WATTGN = CalibRegType.WATTGN
WATTOS = CalibRegType.WATTOS
CHGN_V = CalibRegType.CHGN_V
CHOS_V = CalibRegType.CHOS_V

# 为每种校准类型创建映射
CALIB_REG_TYPE = {
    "CHGN": CHGN,
    "CHOS": CHOS,
    "RMSGN": RMSGN,
    "RMSOS": RMSOS,
    "WATTGN": WATTGN,
    "WATTOS": WATTOS,
    "CHGN_V": CHGN_V,
    "CHOS_V": CHOS_V,
}

# ID常量定义
CONF_BL0906_FACTORY_ID = "bl0906_factory_id"

# 校准寄存器配置常量
CONF_CALIBRATION = "calibration"
CONF_VOLTAGE = "voltage"

# 优化：使用动态生成通道常量，减少重复定义
CHANNEL_CONFIGS = {f"channel_{i}": f"channel_{i}" for i in range(1, 7)}
# 为了向后兼容，保留单独的常量定义
CONF_CHANNEL_1 = CHANNEL_CONFIGS["channel_1"]
CONF_CHANNEL_2 = CHANNEL_CONFIGS["channel_2"]
CONF_CHANNEL_3 = CHANNEL_CONFIGS["channel_3"]
CONF_CHANNEL_4 = CHANNEL_CONFIGS["channel_4"]
CONF_CHANNEL_5 = CHANNEL_CONFIGS["channel_5"]
CONF_CHANNEL_6 = CHANNEL_CONFIGS["channel_6"]
CONF_CURRENT_GAIN = "current_gain"      # CHGN 电流增益
CONF_CURRENT_OFFSET = "current_offset"  # CHOS 电流偏置
CONF_VOLTAGE_GAIN = "voltage_gain"      # CHGN_V 电压增益
CONF_VOLTAGE_OFFSET = "voltage_offset"  # CHOS_V 电压偏置
CONF_RMS_GAIN = "rms_gain"              # RMSGN 有效值增益
CONF_RMS_OFFSET = "rms_offset"          # RMSOS 有效值偏置
CONF_POWER_GAIN = "power_gain"          # WATTGN 功率增益
CONF_POWER_OFFSET = "power_offset"      # WATTOS 功率偏置

# 校准类型映射
CALIB_TYPE_MAP = {
    CONF_CURRENT_GAIN: "CHGN",
    CONF_CURRENT_OFFSET: "CHOS",
    CONF_RMS_GAIN: "RMSGN",
    CONF_RMS_OFFSET: "RMSOS",
    CONF_POWER_GAIN: "WATTGN",
    CONF_POWER_OFFSET: "WATTOS",
    CONF_VOLTAGE_GAIN: "CHGN_V",
    CONF_VOLTAGE_OFFSET: "CHOS_V",
}

# 每个通道的校准配置模式
CHANNEL_CALIBRATION_SCHEMA = cv.Schema({
    cv.Optional(CONF_CURRENT_GAIN): cv.int_range(min=-32768, max=32767),
    cv.Optional(CONF_CURRENT_OFFSET): cv.int_range(min=-32768, max=32767),
    cv.Optional(CONF_RMS_GAIN): cv.int_range(min=-32768, max=32767),
    cv.Optional(CONF_RMS_OFFSET): cv.int_range(min=-32768, max=32767),
    cv.Optional(CONF_POWER_GAIN): cv.int_range(min=-32768, max=32767),
    cv.Optional(CONF_POWER_OFFSET): cv.int_range(min=-32768, max=32767),
})

# 电压通道校准配置模式
VOLTAGE_CALIBRATION_SCHEMA = cv.Schema({
    cv.Optional(CONF_VOLTAGE_GAIN): cv.int_range(min=-32768, max=32767),
    cv.Optional(CONF_VOLTAGE_OFFSET): cv.int_range(min=-32768, max=32767),
})

# 优化：动态生成校准配置模式，减少重复代码
def build_calibration_schema():
    """动态构建校准配置模式"""
    schema_dict = {}

    # 添加通道1-6的校准配置
    for i in range(1, 7):
        channel_key = f"channel_{i}"
        schema_dict[cv.Optional(channel_key)] = CHANNEL_CALIBRATION_SCHEMA

    # 添加电压通道校准配置
    schema_dict[cv.Optional(CONF_VOLTAGE)] = VOLTAGE_CALIBRATION_SCHEMA

    return cv.Schema(schema_dict)

# 整体校准配置模式
CALIBRATION_SCHEMA = build_calibration_schema()

# 组件配置模式
CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Optional(CONF_CALIBRATION): CALIBRATION_SCHEMA,
}).extend(cv.polling_component_schema("60s")).extend(uart.UART_DEVICE_SCHEMA)

FINAL_VALIDATE_SCHEMA = uart.final_validate_device_schema("bl0906_factory")

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    await uart.register_uart_device(var, config)

    # 添加自定义组件标识
    cg.add_define("USE_BL0906_CALIB")

    # 处理校准配置
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]

        # 通道校准值处理函数
        def process_channel_calibration(ch_config, channel_num):
            for conf_key, value in ch_config.items():
                if conf_key in CALIB_TYPE_MAP:
                    calib_type = CALIB_TYPE_MAP[conf_key]
                    cg.add(var.set_initial_calib_value(
                        CALIB_REG_TYPE[calib_type],
                        channel_num,
                        value
                    ))

        # 处理通道1-6的校准值
        for channel in range(1, 7):
            channel_key = f"channel_{channel}"
            if channel_key in calib_config:
                process_channel_calibration(calib_config[channel_key], channel)

        # 处理电压通道校准值
        if CONF_VOLTAGE in calib_config:
            process_channel_calibration(calib_config[CONF_VOLTAGE], 0)

# COMPONENTS 和导入语句已注释掉，保持不变

COMPONENT_SCHEMA = cv.Schema({
    cv.Required(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})

# 平台注册方法定义
SENSOR_PLATFORM = lambda: cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})

NUMBER_PLATFORM = lambda: cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})
