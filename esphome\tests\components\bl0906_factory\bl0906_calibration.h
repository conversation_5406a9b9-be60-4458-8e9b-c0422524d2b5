#pragma once

namespace esphome {
namespace bl0906_factory {

// 转换系数和校正常量
static constexpr int Rt = 2000;
static constexpr float K_p_sum = 16*1.097*1.097*(20000+20000+20000+20000+20000)/(40.41259*((5.1+5.1)*1000/Rt)*1*100*1*1000);//总功率转换*16
static constexpr float K_e_sum = 16*4194304*0.032768*16/(3600000*16*(40.4125*((5.1+5.1)*1000/Rt)*1*100*1*1000/(1.097*1.097*(20000+20000+20000+20000+20000))));//总电量转换*16
static constexpr float FREF = 10000000;//频率转换
static constexpr float TREF = 12.5/59-40;//温度转换 内部温度=（TPS-64）*12.5/59-40 （℃）
static constexpr float K_i = 1.097/(12875*1*(5.1+5.1)*1000/Rt); //电流值转换
static constexpr float K_v = 1.097*(20000+20000+20000+20000+20000)/(13162*1*100*1000); //电压值转换
static constexpr float K_p = 1.097*1.097*(20000+20000+20000+20000+20000)/(40.41259*((5.1+5.1)*1000/Rt)*1*100*1*1000); //功率值转换
static constexpr float K_e = 4194304*0.032768*16/(3600000*16*(40.4125*((5.1+5.1)*1000/Rt)*1*100*1*1000/(1.097*1.097*(20000+20000+20000+20000+20000)))); //电量值转换


}  // namespace bl0906_factory
}  // namespace esphome