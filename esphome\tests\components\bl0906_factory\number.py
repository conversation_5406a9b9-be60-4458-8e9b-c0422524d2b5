import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import number
from esphome.const import (
    CONF_ID,
    ICON_EMPTY,
    UNIT_EMPTY
)

# 从父模块导入所需类和常量
from . import BL0906Factory, BL0906<PERSON><PERSON>ber, CONF_BL0906_FACTORY_ID, bl0906_factory_ns

DEPENDENCIES = ["bl0906_factory"]

# 获取C++中定义的寄存器常量，减少代码重复
# 电流通道增益寄存器
BL0906_CHGN_1 = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_1")
BL0906_CHGN_2 = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_2")
BL0906_CHGN_3 = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_3")
BL0906_CHGN_4 = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_4")
BL0906_CHGN_5 = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_5")
BL0906_CHGN_6 = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_6")
BL0906_CHGN_V = cg.global_ns.namespace("esphome").namespace("bl0906_factory").namespace("BL0906_CHGN_V")

# 定义通用常量
CHANNEL_COUNT = 6

# 使用列表推导式定义配置键
# CHGN 和 CHOS 十进制值系列
CHGN_DECIMAL_KEYS = [f"chgn_decimal_{i}" for i in range(1, CHANNEL_COUNT + 1)] + ["chgn_v_decimal"]
CHOS_DECIMAL_KEYS = [f"chos_decimal_{i}" for i in range(1, CHANNEL_COUNT + 1)] + ["chos_v_decimal"]

# RMSGN 和 RMSOS 十进制值系列
RMSGN_DECIMAL_KEYS = [f"rmsgn_decimal_{i}" for i in range(1, CHANNEL_COUNT + 1)]
RMSOS_DECIMAL_KEYS = [f"rmsos_decimal_{i}" for i in range(1, CHANNEL_COUNT + 1)]

# 构建基本配置模式
CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})

# 优化：使用统一的函数添加Number配置，减少重复代码
def add_number_configs(schema, key_list):
    """统一添加Number配置的辅助函数"""
    for key in key_list:
        schema = schema.extend({
            cv.Optional(key): number.number_schema(
                class_=BL0906Number,
                icon=ICON_EMPTY,
                unit_of_measurement=UNIT_EMPTY,
            ),
        })
    return schema

# 使用统一函数添加所有Number配置
CONFIG_SCHEMA = add_number_configs(CONFIG_SCHEMA, CHGN_DECIMAL_KEYS + CHOS_DECIMAL_KEYS)
CONFIG_SCHEMA = add_number_configs(CONFIG_SCHEMA, RMSGN_DECIMAL_KEYS + RMSOS_DECIMAL_KEYS)

async def to_code(config):
    bl0906_var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 优化：使用统一的Number注册函数
    async def register_number(key, reg_expression):
        """统一的Number注册辅助函数"""
        if key in config:
            conf = config[key]
            num = await number.new_number(
                conf,
                min_value=-32768,
                max_value=32767,
                step=1.0,
            )
            reg_addr = cg.RawExpression(reg_expression)
            cg.add(num.set_register_address(reg_addr))
            cg.add(bl0906_var.register_calib_number(num))

    # 注册CHGN系列（通道1-6）
    for i in range(1, CHANNEL_COUNT + 1):
        await register_number(f"chgn_decimal_{i}", f"esphome::bl0906_factory::BL0906_CHGN_{i}")
        await register_number(f"chos_decimal_{i}", f"esphome::bl0906_factory::BL0906_CHOS_{i}")
        await register_number(f"rmsgn_decimal_{i}", f"esphome::bl0906_factory::BL0906_RMSGN_{i}")
        await register_number(f"rmsos_decimal_{i}", f"esphome::bl0906_factory::BL0906_RMSOS_{i}")

    # 注册电压通道
    await register_number("chgn_v_decimal", "esphome::bl0906_factory::BL0906_CHGN_V")
    await register_number("chos_v_decimal", "esphome::bl0906_factory::BL0906_CHOS_V")